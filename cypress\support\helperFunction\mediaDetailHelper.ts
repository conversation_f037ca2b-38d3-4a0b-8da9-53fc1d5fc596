interface WaitForJobOptions {
  maxRetryAttempts: number;
  retryCount?: number;
  _targetId?: JQuery<HTMLElement>;
}

export const enum VideoName {
  lucyVideo = 'lucy.mp4',
  TestVideo = 'upload_test.mp4',
  FullObjectVideo = 'fullObject.mp4',
}

export const INGESTION = 'Ingestion process';

export const enum Colors {
  Gray = 'rgb(68, 68, 68)',
  DarkRed = 'rgb(183, 28, 28)',
  PastelRed = 'rgb(255, 68, 68)',
  DarkYellow = 'rgb(253, 216, 53)',
  LightBlack = 'rgb(34, 34, 34)',
  Black = 'rgb(0, 0, 0)',
  White = 'rgb(255, 255, 255)',
  Gold = 'rgb(255, 235, 59)',
}

export const enum CSSElement {
  Color = 'color',
  BorderColor = 'border-color',
}

export const enum DataTestSelector {
  FileDetailTimeline = '@file-detail-timeline',
  OverlayContainer = '@overlay-container',
  ClusterRow = 'clusterList-row',
  TimePeriodSpan = '@time-period-span',
  FoundObjectPlace = '@found-objects-place',
  ResultTabVideo = '@video-result-tab',
  ResultTabSaveBtn = '@results-tab-save-button',
  ResultTabRedactBtn = '@results-tab-redact-file-button',
  FileDetailPreview = '@file-detail-preview',
  MovingBackground = '@moving-background',
  videoTabBtn = '@video-tab-button',
  audioTabBtn = '@audio-tab-button',
  RedactModalDelBtn = '@delete-redaction-modal-delete-button',
  InterpolationTabBtn = '@interpolation-tab-button',
  InterpolationSettingsModalSaveBtn = '@interpolation-duration-setting-modal-save-button',
  InterpolationSettingsModalCancelBtn = '@interpolation-duration-setting-modal-cancel-button',
  GlobalSettingsModalTitle = '@global-settings-modal-dialog-title',
  AcceptIcon = 'click-to-edit-icon-check',
  DenyIcon = 'click-to-edit-icon-clear',
  EditorTab = 'results-tab-button',
  ExpandMoreButton = 'ExpandMoreIcon',
  NotificationWindow = 'notificationWindow',
  ShortcutsHeaderDialog = 'keyboard-shortcut-modal-dialog-title',
  keyboardShortcutButton = 'keyboard-shortcut-modal-button',
  SettingsButtonClose = 'interpolation-duration-setting-modal-close-button',
  VideoMessages = 'faces-null-state-tab-message',
  DetectObjectButton = 'faces-null-state-detect-faces-button',
  CheckBoxOutlineBlankIcon = 'CheckBoxOutlineBlankIcon',
  DetectOptionDialog = 'detect-options-dialog-content',
  DetectCountineButton = 'detect-options-face-detection-modal-ok-button',
  CheckBox = 'check-box-container',
  HomePage = 'tdos-grid-container',
  ConfirmDialogContent = 'confirm-dialog-content',
  ConfirmDialogButton = 'confirm-dialog-button',
  ConfirmDialogTitle = 'confirm-dialog-title',
  ArrowLeftIcon = 'ArrowLeftIcon',
  CancelButtonModal = 'save-modal-discard-button',
  GroupLabel = 'demo-row-radio-buttons-group-label',
  SaveModalButton = 'save-modal-save-button',
  CheckedBox = 'checkbox-svg',
}

export const enum DataTestSelectorVideoTab {
  RedactionPerson2 = '0-200-person_0-4',
  RedactionPerson1 = '0-480-person_0-3',
  RedactionHead1 = '0-440-face_0-2',
  RedactionHead2 = '0-240-face_0-1',
  ObjectRedaction = 'rnd-box',
  SearchRedactionCodeField = 'searchRedactionCode',
  RestoreDefaultColorButton = 'restore-default-color-btn',
  VideoPlayScreen = 'media-player-container',
  VideoControlBar = 'video-default-control-bar',
  ShapesMenu = 'Shapes-Menu',
}

export const enum DataTestSelectorTimeline {
  PreviewObject = 'form-control-label',
  HeadValue = 'head-resize-value',
  PersonValue = 'person-resize-value',
  MeasuringBar = 'media-detail-slider',
  VideoPreviewNoneInput = 'form-control-label-none',
  VideoPreviewBlackFillInput = 'form-control-label-black_fill',
  VideoPreviewOutlineInput = 'form-control-label-outline',
  VideoPreviewRedactedInput = 'form-control-label-redacted',
  TimeLinePositionBar = 'timeline-position-bar',
  TimeLinePositionBarSelect = 'timeline-position-bar-select',
  TimeLinePlayHead = 'timeline-play-head',
  TotalFilter = 'total-filter',
  TimeLineContainer = 'time-line-container',
  TimeLineFilter = 'timeline-filter-list-popover',
  TimelineRow = 'timeline-row',
}

export const enum DataTestSelectorRightPanelVideo {
  CommentsTab = 'comments-tab-button',
  AudioTab = 'audio-tab-button',
  EngineProcessingTitle = 'engine-processing-title',
  EngineProcessingMessage = 'typography-body2',
  DetectFaceButton = 'faces-list-detect-faces-button',
  RedactionEffectsDropdown = 'bulk-redaction-button',
  FilterDetectionsButton = 'filter-button',
  CheckBoxContainer = 'check-box-container',
  SortByTimeButton = 'box-sort-start-time-ms',
  ListOfObjects = 'virtuoso-item-list',
  PinToTopOption = 'pin-to-top-option',
  SelectOption = 'select-option',
  MergedWidthPinnedGroupOption = 'merge-with-pinned-group-option',
  DeleteGroupOption = 'delete-group-option',
  CardPin = 'cluster-card-pin',
  MergeGroupPopup = 'merge-group-popup',
  MergeGroupOption = 'merge-group-option-',
  ListSortContainer = 'cluster-list-sort-container',
  IndeterminateSvg = 'indeterminate-svg',
  MergedWidthNamedGroup = 'merge-with-named-group-option',
  NameGroup = 'click-to-edit-span',
  NameGroupInput = 'click-to-edit-input',
  GroupItemMenu = 'cluster-card-menu',
  DetectionName = 'detection-name',
  ObjectItemContainer = 'cluster-group-container',
  UDRFilterCheckbox = 'udr-filter-show-checkbox',
  HeaderFilterCheckbox = 'head-filter-show-checkbox',
  PersonFilterCheckbox = 'person-filter-show-checkbox',
  AllFilterCheckbox = 'all-filter-show-checkbox',
  FilterDetectionsDialog = 'cluster-list-filter-menu-popover',
  RightPanelFilterContainer = 'cluster-list-filter-container',
  UDRFilterRemoveCheckBox = 'udr-filter-remove-checkbox',
  HeadFilterRemoveCheckBox = 'head-filter-remove-checkbox',
  PersonFilterRemoveCheckBox = 'person-filter-remove-checkbox',
  RedactionDetectionType = 'redaction-detection-type',
  // DetectionTypeMenu = 'redaction-detection-type-menu',
  // BlurTypeMenu = 'redaction-blur-type-menu',
  RedactionEffectsDialog = 'bulk-redaction-menu-popover',
  RedactionType = 'bulk-redaction-redaction-type',
  BlurLevel = 'bulk-redaction-level-input',
  RestoreDefaultButton = 'bulk-redaction-default-button',
  CancelButton = 'bulk-redaction-cancel-button',
  SaveButtonRedaction = 'bulk-redaction-save-button',
}

export const enum DataTestSelectorMessages {
  DetectingObjects = 'Detecting Objects...',
  DetectingObjectsMessage1 = 'This may take some time depending on video length.',
  DetectingObjectsMessage2 = 'You can wait for the processing to finish here',
  DetectingObjectsMessage3 = 'or go back to the',
}

export const enum DataVeritoneSelector {
  RedactFileTabBtn = 'redacted-files-tab-button',
  RedactFileDelBtn = 'redacted-files-delete-button',
  MediaDetailCloseBtn = 'media-details-close-button',
  MediaSettingsBtn = 'media-details-settings-button',
  randomWord = '[data-testid="display-view-span"]:eq(4)',
  NotificationButton = 'notification-button',
  ShortcutsButton = 'results-tab-shortcuts-button',
  FileName = 'filename',
  Status = 'current-file-stage',
  Header = 'media-details-top-bar',
  File = 'tdo-wrapper',
  UndoButton = 'results-tab-undo-button',
  RedoButton = 'results-tab-redo-button',
  HelpCenterButton = 'help-link-button',
  SwitchShowUnselected = 'switch-show-unselected',
  HomePageButton = 'loading-state-home-page-button',
  MediaDetailPage = 'media-detail-container',
}

export const enum Graphql {
  GraphqlURL = '/v3/graphql',
  JobQuery = `query($jobId: ID!) {\n      job(id: $jobId) {\n        id\n        targetId\n        status\n      }\n    }`,
  AudioQuery = `\n      query($tdoId: ID!, $engines: [ID!], $startOffset: Int, $stopOffset: Int) {\n        engineResults(tdoId: $tdoId, engineIds: $engines, startOffsetMs: $startOffset, stopOffsetMs: $stopOffset) {\n          records {\n            tdoId\n            engineId\n            startOffsetMs\n            stopOffsetMs\n            jsondata\n          }\n        }\n      }\n    `,
  AddBoundingBox = 'mutation ($payload: JSONData!, $application: String){\n      emitAuditEvent (input: {\n        application: $application\n        payload: $payload\n      }) {\n        id\n      }\n    }\n  ',
  JobQueryNoTargetId = 'query($jobId: ID!) {\n      job(id: $jobId) {\n        status\n        createdDateTime\n      }\n    }',
  ListAssetsQuery = `
  query listAssetByTypes($tdoId: ID!) {
    temporalDataObject(id: $tdoId) {
      assets(assetType: ["redaction-user-selections", "redaction-media-data", "redacted-media"]) {
        records {
          id
          assetType
          isUserEdited
        }
      }
    }
  }`,
  GetAssetsQuery = `
  query getAssets($tdoId: ID!) {
    temporalDataObject(id: $tdoId) {
      assets(assetType: ["redacted-media", "redaction-audit-log", "redaction-export"]) {
        records {
          id
          assetType
        }
      }
    }
  }`,
  updateTDO = `
      mutation updateTDO($tdoId: ID!, $details: JSONData) {
        updateTDO(input: {
          id: $tdoId
          details: $details
        }) {
          id
          details
        }
      }
    `,
  getTDODetails = `
    query getTDODetails($tdoId: ID!) {
      temporalDataObject(id: $tdoId) {
        id
        details
      }
    }
  `,
}

export const enum VideoResult {
  OverlayGroup = 'Overlay Group',
}
export const enum TimeRange {
  TimeRange0 = '00:00 - 00:00',
}

export enum MediaFieldName {
  AutoField = 'Auto Interpolation',
  Manual = 'Manual Interpolation',
  Offset = 'Video Offset',
}

export enum UdrMenuList {
  AddRedactionEffect = 'Add Redaction Effect',
  EditRedactionEffect = 'Edit Redaction Effect',
  RemoveRedactionEffect = 'Remove Redaction Effect',
  StickVideo = 'Stick to video',
  AddRedactionCode = 'Add Redaction Code',
  SetTimeStamp = 'Set Time Stamp',
  FrameDelete = 'Delete UDR in Frame',
  OverlayDelete = 'Delete UDR Overlay',
  SegmentDelete = 'Delete UDR Segment',
  GroupDelete = 'Delete UDR Group',
  TrackObject = 'TrackObject',
  TrackForward = 'Track forward only',
  TrackBackward = 'Track backward only',
}

export enum RedactionEffect {
  Blur = 'Blur',
  BlackFill = 'Blackfill',
  Outline = 'Outline',
}

export enum UdrShape {
  Ellipse = 'ellipse',
  Rectangle = 'Rectangle',
}

export enum SettingsTabName {
  ObjectDetect = 'Object Detection',
  Tracking = 'Tracking',
  Interpolation = 'Interpolation',
  Redaction = 'Redaction',
}

export const dataIdPopupMap: { [key: string]: string } = {
  'Add Code': 'add-redaction-code-menu',
  'Edit Code': 'add-redaction-code-menu',
  'Custom Redaction Effect': 'blur-config-menu',
};

export function ordinalToNumber(ordinal: string): number | undefined {
  const ordinalRegex = /^(\d+)(st|nd|rd|th)$/i;
  const match = ordinal.match(ordinalRegex);
  if (match && match[1]) {
    return parseInt(match[1], 10);
  }

  return undefined;
}

export function waitForJobSuccess(options: WaitForJobOptions) {
  const { maxRetryAttempts, retryCount = 0, _targetId } = options;

  cy.wait('@jobStatus', { timeout: 40000 }).then((interception) => {
    const response = interception.response?.body?.data?.job;

    if (!response) {
      cy.log('Error: response is invalid or empty.');
      return;
    }

    if (_targetId) {
      if (response.targetId) {
        if (response.targetId === _targetId) {
          if (response.status === 'complete') {
            cy.log(`Job status with ${_targetId} is success!`);
            return;
          } else if (response.status === 'failed') {
            cy.log(`Job with targetId ${_targetId} failed.`);
            return;
          } else {
            if (retryCount < maxRetryAttempts) {
              cy.log(
                `Job status with targetId ${_targetId} is ${response.status}. Retrying... (Attempt ${retryCount + 1})`
              );
              waitForJobSuccess({
                maxRetryAttempts: maxRetryAttempts,
                retryCount: retryCount + 1,
                _targetId,
              });
            } else {
              cy.log(
                `Job with targetId ${_targetId} did not complete within ${maxRetryAttempts} retries.`
              );
              return;
            }
          }
        }
      }
    } else {
      if (response.status === 'complete') {
        cy.log('Job status is success!');
        return;
      } else if (response.status === 'failed') {
        cy.log(`Job failed.`);
        return;
      } else {
        if (retryCount < maxRetryAttempts) {
          cy.log(
            `Job status is ${response.status}. Retrying... (Attempt ${retryCount + 1})`
          );
          waitForJobSuccess({
            maxRetryAttempts: maxRetryAttempts,
            retryCount: retryCount + 1,
          });
        } else {
          cy.log(`Job did not complete within ${maxRetryAttempts} retries.`);
          cy.wrap(true).should('be.false');
          return;
        }
      }
      return;
    }
    return;
  });
}

export function openAndSetInterpValues(auto: InterpolationValue, manual: InterpolationValue) {
  cy.get('[data-veritone-element="media-details-settings-button"]').click();
  cy.get('[data-testid="interpolation-tab-button"]').click();
  cy.get('[name="autoInterpolationMax"]').as('auto');
  cy.get('@auto').clear();
  cy.get('@auto').type(auto);
  cy.get('[name="manualInterpolationMax"]').as('manual');
  cy.get('@manual').clear();
  cy.get('@manual').type(manual);
  //   cy.get('#videoOffset').as('offset');
  //   cy.get('@offset').clear();
  //   cy.get('@offset').type(offset);
  cy.get(
    '[data-testid="interpolation-duration-setting-modal-save-button"]'
  ).click();
}
